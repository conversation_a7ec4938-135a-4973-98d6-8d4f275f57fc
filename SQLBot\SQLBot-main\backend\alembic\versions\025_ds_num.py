"""025_ds_num

Revision ID: 97dcdbedaaf3
Revises: 4c6d18a18bd4
Create Date: 2025-07-15 15:50:56.942959

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '97dcdbedaaf3'
down_revision = '806bc67ff45f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('core_datasource', sa.Column('num', sqlmodel.sql.sqltypes.AutoString(length=256), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('core_datasource', 'num')
    # ### end Alembic commands ###
