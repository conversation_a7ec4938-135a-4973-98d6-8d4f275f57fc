services:
  sqlbot:
    image: registry.cn-qingdao.aliyuncs.com/dataease/sqlbot:SQLBOT_TAG
    container_name: sqlbot
    restart: always
    networks:
      - sqlbot-network
    ports:
      - ${SQLBOT_WEB_PORT}:8000
      - ${SQLBOT_MCP_PORT}:8001
    env_file:
      - conf/sqlbot.conf
    volumes:
      - ./data/sqlbot/excel:/opt/sqlbot/data/excel
      - ./data/sqlbot/images:/opt/sqlbot/images
      - ./data/sqlbot/logs:/opt/sqlbot/logs
    depends_on:
      sqlbot-db:
        condition: service_healthy

networks:
  sqlbot-network:
