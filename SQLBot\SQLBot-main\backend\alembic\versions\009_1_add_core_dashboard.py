"""core_dashboard

Revision ID: 804b08ac329d
Revises: 1f077c30e476
Create Date: 2025-06-03 14:40:25.419409

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '804b08ac329d'
down_revision = '1f077c30e476'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('core_dashboard',
    sa.Column('id', sa.String(length=50), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('pid', sa.String(length=50), nullable=True),
    sa.Column('workspace_id', sa.String(length=50), nullable=True),
    sa.Column('org_id', sa.String(length=50), nullable=True),
    sa.Column('level', sa.Integer(), nullable=True),
    sa.Column('node_type', sa.String(length=255), nullable=True),
    sa.Column('type', sa.String(length=50), nullable=True),
    sa.Column('canvas_style_data', sa.Text(), nullable=True),
    sa.Column('component_data', sa.Text(), nullable=True),
    sa.Column('mobile_layout', sa.SmallInteger(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.Column('self_watermark_status', sa.Integer(), nullable=True),
    sa.Column('sort', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.BigInteger(), nullable=True),
    sa.Column('create_by', sa.String(length=255), nullable=True),
    sa.Column('update_time', sa.BigInteger(), nullable=True),
    sa.Column('update_by', sa.String(length=255), nullable=True),
    sa.Column('remark', sa.String(length=255), nullable=True),
    sa.Column('source', sa.String(length=255), nullable=True),
    sa.Column('delete_flag', sa.SmallInteger(), nullable=True),
    sa.Column('delete_time', sa.BigInteger(), nullable=True),
    sa.Column('delete_by', sa.String(length=255), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.Column('content_id', sa.String(length=50), nullable=True),
    sa.Column('check_version', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    op.drop_table('core_dashboard')
    # ### end Alembic commands ###
