# 基础配置
## 安装目录
SQLBOT_BASE=/opt
## SQLBot 端口
SQLBOT_WEB_PORT=8000
SQLBOT_MCP_PORT=8001

# 数据库配置
## 是否使用外部数据库
SQLBOT_EXTERNAL_DB=false
## 数据库地址
SQLBOT_DB_HOST=sqlbot-db
## 数据库端口 (仅使用外部数据库时才生效)
SQLBOT_DB_PORT=5432
## SQLBot 数据库库名
SQLBOT_DB_DB=sqlbot
## 数据库用户名
SQLBOT_DB_USER=root
## 数据库密码，密码如包含特殊字符，请用双引号引起来，例如 SQLBOT_DB_PASSWORD="Test@4&^%*^"
SQLBOT_DB_PASSWORD=Password123@pg

# 其他配置
## 普通用户默认密码
SQLBOT_DEFAULT_PWD=SQLBot@123456
## SQLBot Secret Key
SQLBOT_SECRET_KEY=y5txe1mRmS_JpOrUzFzHEu-kIQn3lf7ll0AOv9DQh0s
## Cross-Origin Resource Sharing (CORS) 设置
SQLBOT_CORS_ORIGINS=http://localhost,http://localhost:5173,https://localhost,https://localhost:5173
## 日志级别 DEBUG, INFO, WARNING, ERROR
SQLBOT_LOG_LEVEL="INFO"
## 缓存类型
SQLBOT_CACHE_TYPE="memory"
## MCP 图片存储路径
SQLBOT_SERVER_IMAGE_HOST=https://YOUR_SERVER_IP:MCP_PORT/images/