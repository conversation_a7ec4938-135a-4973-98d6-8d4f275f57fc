"""003 add datasource

Revision ID: d116056121c3
Revises: 1c8bcc7e25c8
Create Date: 2025-05-06 15:13:06.058032

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'd116056121c3'
down_revision = '1c8bcc7e25c8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('core_datasource',
    sa.<PERSON>umn('id', sa.Integer(), sa.Identity(always=True), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=512), nullable=True),
    sa.Column('type', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('configuration', sa.Text(), nullable=True),
    sa.Column('create_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('create_by', sa.BigInteger(), nullable=True),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('core_datasource')
    # ### end Alembic commands ###
