<script lang="ts" setup>
import { ref, shallowRef, computed } from 'vue'
import icon_searchOutline_outlined from '@/assets/svg/icon_search-outline_outlined.svg'
import EmptyBackground from '@/views/dashboard/common/EmptyBackground.vue'
import { dsTypeWithImg } from './js/ds-type'

interface Datasource {
  name: string
  type: string
  img: string
  rate?: string
  id?: string
}
const keywords = ref('')
const modelList = shallowRef(dsTypeWithImg as Datasource[])
const modelListWithSearch = computed(() => {
  if (!keywords.value) return modelList.value
  return modelList.value.filter((ele) =>
    ele.name.toLowerCase().includes(keywords.value.toLowerCase())
  )
})
const emits = defineEmits(['clickDatasource'])
const handleModelClick = (item: any) => {
  emits('clickDatasource', item)
}
</script>

<template>
  <div class="datasouce-list">
    <div class="title">{{ $t('qa.select_datasource') }}</div>
    <el-input
      v-model="keywords"
      clearable
      style="width: 100%; margin-right: 12px"
      :placeholder="$t('datasource.search')"
    >
      <template #prefix>
        <el-icon>
          <icon_searchOutline_outlined class="svg-icon" />
        </el-icon>
      </template>
    </el-input>
    <div class="list-content">
      <div
        v-for="ele in modelListWithSearch"
        :key="ele.name"
        class="model"
        @click="handleModelClick(ele)"
      >
        <img width="32px" height="32px" :src="ele.img" />
        <span class="name">{{ ele.name }}</span>
      </div>
      <EmptyBackground
        v-if="!!keywords && !modelListWithSearch.length"
        :description="$t('datasource.relevant_content_found')"
        img-type="tree"
        style="width: 100%; margin-top: 100px"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
.datasouce-list {
  width: 800px;
  margin: 0 auto;
  max-height: 100%;
  padding-top: 24px;
  .title {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 16px;
  }

  .list-content {
    margin-top: 16px;
    display: flex;
    height: calc(100% - 40px);
    flex-wrap: wrap;

    .model:nth-child(odd) {
      margin-left: 0;
    }

    .model {
      width: 392px;
      height: 64px;
      display: flex;
      align-items: center;
      padding-left: 16px;
      margin-bottom: 16px;
      border: 1px solid #dee0e3;
      border-radius: 12px;
      margin-left: 16px;
      cursor: pointer;
      &:hover {
        box-shadow: 0px 6px 24px 0px #1f232914;
      }
      .name {
        margin-left: 12px;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
      }
    }
  }
}
</style>
