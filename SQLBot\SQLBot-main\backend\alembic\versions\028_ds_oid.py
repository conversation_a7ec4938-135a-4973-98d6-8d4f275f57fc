"""028_ds_oid

Revision ID: e96b16d3daab
Revises: b049c9f8ca5b
Create Date: 2025-07-17 14:40:48.522033

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'e96b16d3daab'
down_revision = 'b049c9f8ca5b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('core_datasource', sa.Column('oid', sa.BigInteger(), nullable=True))
    op.execute('update core_datasource set oid = 1')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('core_datasource', 'oid')
    # ### end Alembic commands ###
