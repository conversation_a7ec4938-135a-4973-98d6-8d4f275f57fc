"""010_modify_chat

Revision ID: bfa10ce83d73
Revises: 8dc3b1bdbfef
Create Date: 2025-06-18 14:16:39.230619

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bfa10ce83d73'
down_revision = 'a3af70d43e98'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_record', sa.Column('analysis', sa.Text(), nullable=True))
    op.add_column('chat_record', sa.Column('predict', sa.Text(), nullable=True))
    op.add_column('chat_record', sa.Column('full_analysis_message', sa.Text(), nullable=True))
    op.add_column('chat_record', sa.Column('full_predict_message', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_record', 'full_predict_message')
    op.drop_column('chat_record', 'full_analysis_message')
    op.drop_column('chat_record', 'predict')
    op.drop_column('chat_record', 'analysis')
    # ### end Alembic commands ###
