"""024_modify_chat_record

Revision ID: 806bc67ff45f
Revises: f535d09946f6
Create Date: 2025-07-11 18:09:52.417628

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '806bc67ff45f'
down_revision = 'f535d09946f6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_record', sa.Column('analysis_record_id', sa.BigInteger(), nullable=True))
    op.add_column('chat_record', sa.Column('predict_record_id', sa.BigInteger(), nullable=True))
    op.drop_column('chat_record', 'run_time')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_record', sa.Column('run_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False))
    op.drop_column('chat_record', 'predict_record_id')
    op.drop_column('chat_record', 'analysis_record_id')
    # ### end Alembic commands ###
