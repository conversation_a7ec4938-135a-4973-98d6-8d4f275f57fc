"""019_upgrade_model

Revision ID: dcaecd481715
Revises: 863105882eba
Create Date: 2025-07-04 14:45:56.927188

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dcaecd481715'
down_revision = '863105882eba'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index(op.f('ix_ai_model_id'), table_name='ai_model')
    op.drop_table('ai_model')
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_model',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('api_key', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('api_domain', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('protocol', sa.Integer(), nullable=False),
    sa.Column('supplier', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('model_type', sa.Integer(), nullable=False),
    sa.Column('base_model', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('default_model', sa.Boolean(), nullable=False),
    sa.Column('config', sa.Text(), nullable=False),
    sa.Column('status', sa.Integer(), nullable=False),
    sa.Column('create_time', sa.BigInteger(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_model_id'), 'ai_model', ['id'], unique=False)
    # ### end Alembic commands ###

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ai_model_id'), table_name='ai_model')
    op.drop_table('ai_model')
    
    op.create_table('ai_model',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('type', sa.Integer(), nullable=False),
    sa.Column('api_key', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('endpoint', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('max_context_window', sa.Integer(), nullable=False),
    sa.Column('temperature', sa.Float(), nullable=False),
    sa.Column('status', sa.Boolean(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('create_time', sa.BigInteger(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_model_id'), 'ai_model', ['id'], unique=False)
    # ### end Alembic commands ###
