"""033_chat_origin_ddl

Revision ID: 3cb5d6a54f2e
Revises: 6549e47f9adc
Create Date: 2025-07-22 22:00:48.599729

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3cb5d6a54f2e'
down_revision = '6549e47f9adc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat', sa.Column('origin', sa.Integer(), nullable=True, default=0))  # 0: default, 1: mcp, 2: assistant
    op.execute('UPDATE chat SET origin = 0 WHERE origin IS NULL')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat', 'origin')
    # ### end Alembic commands ###
