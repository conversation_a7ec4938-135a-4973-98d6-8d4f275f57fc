"""009_modify_chat

Revision ID: 1f077c30e476
Revises: 35d925df4568
Create Date: 2025-05-30 16:11:08.020715

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1f077c30e476'
down_revision = '35d925df4568'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('chat_record')
    op.create_table('chat_record',
    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
    sa.Column('chat_id', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('finish_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('create_by', sa.<PERSON>(), nullable=True),
    sa.Column('datasource', sa.Integer(), nullable=False),
    sa.Column('engine_type', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('question', sa.Text(), nullable=True),
    sa.Column('sql_answer', sa.Text(), nullable=True),
    sa.Column('sql', sa.Text(), nullable=True),
    sa.Column('sql_exec_result', sa.Text(), nullable=True),
    sa.Column('data', sa.Text(), nullable=True),
    sa.Column('chart_answer', sa.Text(), nullable=True),
    sa.Column('chart', sa.Text(), nullable=True),
    sa.Column('full_sql_message', sa.Text(), nullable=True),
    sa.Column('full_chart_message', sa.Text(), nullable=True),
    sa.Column('finish', sa.Boolean(), nullable=True),
    sa.Column('error', sa.Text(), nullable=True),
    sa.Column('run_time', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('chat_record')
    op.create_table('chat_record',
    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
    sa.Column('chat_id', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('create_by', sa.BigInteger(), nullable=True),
    sa.Column('datasource', sa.Integer(), nullable=False),
    sa.Column('engine_type', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('question', sa.Text(), nullable=True),
    sa.Column('full_question', sa.Text(), nullable=True),
    sa.Column('answer', sa.Text(), nullable=True),
    sa.Column('run_time', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('id')
                    )
    # ### end Alembic commands ###
