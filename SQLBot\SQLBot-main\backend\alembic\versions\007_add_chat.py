"""006_add_chat

Revision ID: ff653d5df198
Revises: 0a6f11be9be4
Create Date: 2025-05-21 15:44:29.763091

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ff653d5df198'
down_revision = 'e6276ddab06e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chat',
    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
    sa.Column('create_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('create_by', sa.BigInteger(), nullable=True),
    sa.Column('brief', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=True),
    sa.Column('chat_type', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('datasource', sa.Integer(), nullable=False),
    sa.Column('engine_type', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('chat_record',
    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
    sa.Column('chat_id', sa.Integer(), nullable=True),
    sa.Column('create_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('create_by', sa.BigInteger(), nullable=True),
    sa.Column('datasource', sa.Integer(), nullable=False),
    sa.Column('engine_type', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.Column('question', sa.Text(), nullable=True),
    sa.Column('full_question', sa.Text(), nullable=True),
    sa.Column('answer', sa.Text(), nullable=True),
    sa.Column('run_time', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('chat_record')
    op.drop_table('chat')
    # ### end Alembic commands ###
