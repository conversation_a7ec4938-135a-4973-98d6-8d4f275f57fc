"""027_modify_permission

Revision ID: b049c9f8ca5b
Revises: 4c6d18a18bd4
Create Date: 2025-07-16 09:59:23.345135

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b049c9f8ca5b'
down_revision = '4c6d18a18bd4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ds_permission', sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=128), nullable=True))
    op.alter_column('ds_permission', 'auth_target_type',
               existing_type=sa.VARCHAR(length=128),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ds_permission', 'auth_target_type',
               existing_type=sa.VARCHAR(length=128),
               nullable=False)
    op.drop_column('ds_permission', 'name')
    # ### end Alembic commands ###
