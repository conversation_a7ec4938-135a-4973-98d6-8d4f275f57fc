name: package-and-push

run-name: 构建离线安装包 ${{ github.event.inputs.dockerImageTag }}

on:
  workflow_dispatch:
    inputs:
      dockerImageTag:
        description: 'Image Tag'
        default: 'v0.9.5'
        required: true

jobs:
  package-and-push-to-aliyun-oss:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}
      - name: Install ossutil
        run: |
          curl -L https://gosspublic.alicdn.com/ossutil/1.7.0/ossutil64 -o /usr/local/bin/ossutil
          chmod +x /usr/local/bin/ossutil
      - name: Prepare package files
        env:
          TAG_NAME: ${{ github.event.inputs.dockerImageTag }}
          DOCKER_DOWNLOAD_SERVER: ${{ secrets.DOCKER_DOWNLOAD_SERVER }}
          ALIYUN_REGISTRY_HOST: ${{ secrets.ALIYUN_REGISTRY_HOST }}
          ALIYUN_OSS_BUCKET: ${{ secrets.ALIYUN_OSS_BUCKET }}
          ALIYUN_OSS_BUCKET_ENDPOINT: ${{ secrets.ALIYUN_OSS_BUCKET_ENDPOINT }}
          ALIYUN_OSS_ACCESS_KEY: ${{ secrets.ALIYUN_OSS_ACCESS_KEY }}
          ALIYUN_OSS_ACCESS_SECRET: ${{ secrets.ALIYUN_OSS_ACCESS_SECRET }}
        run: |
          DOCKER_IMAGE=${ALIYUN_REGISTRY_HOST}/dataease/sqlbot
          cd installer
          
          echo ${TAG_NAME} > ./sqlbot/templates/version
          sed -i -e "s/SQLBOT_TAG/${TAG_NAME}/g" sqlbot/docker-compose.yml
          mkdir images
          for image in $(grep  "image: " sqlbot/docker*.yml | awk -F 'image:' '{print $2}'); do
            image_path=$(eval echo $image)
            image_name=$(echo ${image_path##*/})
            docker pull $image_path
            docker save $image_path  | gzip > images/$image_name.tar.gz
          done

          platform=""
          ARCH="x86_64"
          DOCKER_VERSION="docker-27.2.0"
          DOCKER_COMPOSE_VERSION="v2.29.2"

          wget ${DOCKER_DOWNLOAD_SERVER}/download/${ARCH}/${DOCKER_VERSION}.tgz
          wget ${DOCKER_DOWNLOAD_SERVER}/compose/releases/download/${DOCKER_COMPOSE_VERSION}/docker-compose-linux-${ARCH} && mv docker-compose-linux-${ARCH} docker-compose && chmod +x docker-compose
          tar -zxvf ${DOCKER_VERSION}.tgz
          rm -rf ${DOCKER_VERSION}.tgz
          mv docker bin && mkdir docker && mv bin docker/
          mv docker-compose docker/bin
          wget https://resource-fit2cloud-com.oss-cn-hangzhou.aliyuncs.com/docker/docker.service
          mkdir docker/service && mv docker.service docker/service/

          #打包离线包
          package_offline="sqlbot-offline-installer-${TAG_NAME}${platform}-ce.tar.gz"
          touch $package_offline
          tar --transform "s/^\./sqlbot-offline-installer-${TAG_NAME}${platform}-ce/" \
          --exclude $package_offline \
          --exclude .git \
          -czvf $package_offline . 

          #打包在线包
          package_online="sqlbot-online-installer-${TAG_NAME}${platform}-ce.tar.gz"
          touch $package_online
          tar --transform "s/^\./sqlbot-online-installer-${TAG_NAME}${platform}-ce/" \
          --exclude $package_online \
          --exclude $package_offline \
          --exclude .git \
          --exclude images \
          --exclude docker \
          -czvf $package_online . 
          
          #Sync files to OSS
          ossutil cp -rf ${package_offline} oss://$ALIYUN_OSS_BUCKET/sqlbot/${package_offline} --access-key-id=$ALIYUN_OSS_ACCESS_KEY --access-key-secret=$ALIYUN_OSS_ACCESS_SECRET --endpoint=${ALIYUN_OSS_BUCKET_ENDPOINT}
          ossutil cp -rf ${package_online} oss://$ALIYUN_OSS_BUCKET/sqlbot/${package_online} --access-key-id=$ALIYUN_OSS_ACCESS_KEY --access-key-secret=$ALIYUN_OSS_ACCESS_SECRET --endpoint=${ALIYUN_OSS_BUCKET_ENDPOINT}

  
  

