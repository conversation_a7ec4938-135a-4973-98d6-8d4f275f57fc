PROJECT_NAME="SQLBot"
# Backend
BACKEND_CORS_ORIGINS=${SQLBOT_CORS_ORIGINS}
SECRET_KEY=${SQLBOT_SECRET_KEY}

DEFAULT_PWD=${SQLBOT_DEFAULT_PWD}

LOG_LEVEL=${SQLBOT_LOG_LEVEL}  # DEBUG, INFO, WARNING, ERROR
SQL_DEBUG=False

CACHE_TYPE=${SQLBOT_CACHE_TYPE}

# Postgres
POSTGRES_SERVER=${SQLBOT_DB_HOST}
POSTGRES_PORT=${SQLBOT_DB_PORT}
POSTGRES_DB=${SQLBOT_DB_DB}
POSTGRES_USER=${SQLBOT_DB_USER}
POSTGRES_PASSWORD=${SQLBOT_DB_PASSWORD} # Change this to your pwd

SERVER_IMAGE_HOST=${SQLBOT_SERVER_IMAGE_HOST}