"""msg...

Revision ID: 941e2355a94d
Revises: 8dc3b1bdbfef
Create Date: 2025-06-18 14:58:21.977676

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '941e2355a94d'
down_revision = '8dc3b1bdbfef'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column('core_dashboard', sa.Column('canvas_view_info', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('core_dashboard', 'canvas_view_info')
    # ### end Alembic commands ###
