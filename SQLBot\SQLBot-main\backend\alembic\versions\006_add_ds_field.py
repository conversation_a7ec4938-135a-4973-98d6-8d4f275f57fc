"""006_add_ds_field

Revision ID: e6276ddab06e
Revises: 0a6f11be9be4
Create Date: 2025-05-22 11:43:40.176878

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e6276ddab06e'
down_revision = '0a6f11be9be4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('core_datasource', sa.Column('type_name', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('core_datasource', 'type_name')
    # ### end Alembic commands ###
