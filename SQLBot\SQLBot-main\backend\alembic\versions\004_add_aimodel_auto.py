"""004_add_aimodel_auto

Revision ID: 8fe654655905
Revises: d116056121c3
Create Date: 2025-05-07 15:51:34.768842

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8fe654655905'
down_revision = 'd116056121c3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ai_model',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('type', sa.Integer(), nullable=False),
    sa.Column('api_key', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.<PERSON>umn('endpoint', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('max_context_window', sa.Integer(), nullable=False),
    sa.Column('temperature', sa.Float(), nullable=False),
    sa.Column('status', sa.Boolean(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('create_time', sa.BigInteger(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ai_model_id'), 'ai_model', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ai_model_id'), table_name='ai_model')
    op.drop_table('ai_model')
    # ### end Alembic commands ###
