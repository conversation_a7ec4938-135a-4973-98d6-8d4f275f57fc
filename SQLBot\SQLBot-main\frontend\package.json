{"name": "SQLBot", "private": true, "version": "0.0.0", "type": "module", "eslintConfig": {"env": {"browser": true, "node": true}}, "scripts": {"dev": "vue-tsc -b && vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix"}, "dependencies": {"@antv/g2": "^5.3.3", "@antv/s2": "^2.4.3", "@eslint/js": "^9.28.0", "@highlightjs/vue-plugin": "^2.1.0", "@npkg/tinymce-plugins": "^0.0.7", "@tinymce/tinymce-vue": "^5.1.0", "dayjs": "^1.11.13", "element-plus": "^2.10.1", "element-plus-secondary": "^1.0.0", "element-resize-detector": "^1.2.4", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "snowflake-id": "^1.1.0", "tinymce": "^7.9.1", "vue": "^3.5.13", "vue-dompurify-html": "^5.3.0", "vue-i18n": "^9.14.4", "vue-router": "^4.5.0", "vue-types": "^6.0.0", "web-storage-cache": "^1.1.1"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@eslint/migrate-config": "^1.5.0", "@types/crypto-js": "^4.2.2", "@types/element-resize-detector": "^1.1.6", "@types/markdown-it": "^14.1.2", "@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "axios": "^1.8.4", "crypto-js": "^4.2.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-vue": "^10.2.0", "globals": "^16.2.0", "less": "^4.3.0", "pinia": "^3.0.2", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.34.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components-secondary": "^0.24.6", "vite": "^6.3.1", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.8"}}