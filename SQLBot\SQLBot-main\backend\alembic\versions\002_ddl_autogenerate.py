"""002_ddl_autogenerate

Revision ID: 1c8bcc7e25c8
Revises: 5348b743b05f
Create Date: 2025-04-25 17:47:18.795288

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '1c8bcc7e25c8'
down_revision = '5348b743b05f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('terms',
    sa.<PERSON>umn('id', sa.BigInteger(), nullable=False),
    sa.Column('term', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('definition', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('domain', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.<PERSON>umn('create_time', sa.Integer(), nullable=False),
    sa.Primary<PERSON>ey<PERSON>onstraint('id')
    )
    op.create_index(op.f('ix_terms_id'), 'terms', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_terms_id'), table_name='terms')
    op.drop_table('terms')
    # ### end Alembic commands ###
