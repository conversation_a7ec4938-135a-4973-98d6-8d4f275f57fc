"""030_permission_oid

Revision ID: c1d7ac00b3a8
Revises: 77d4c39ec22f
Create Date: 2025-07-21 11:49:43.115524

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c1d7ac00b3a8'
down_revision = '77d4c39ec22f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ds_rules', sa.Column('oid', sa.BigInteger(), nullable=True))
    op.alter_column('ds_permission', 'id',
                    existing_type=sa.INTEGER(),
                    type_=sa.BigInteger(),
                    existing_nullable=False,
                    autoincrement=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ds_permission', 'id',
                    existing_type=sa.BigInteger(),
                    type_=sa.INTEGER(),
                    existing_nullable=False,
                    autoincrement=True)
    op.drop_column('ds_rules', 'oid')
    # ### end Alembic commands ###
