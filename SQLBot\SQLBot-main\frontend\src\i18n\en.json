{"menu": {"Data Q&A": "Data Q&A", "Data Connections": "Data Sources", "Dashboard": "Dashboard", "AI Model Configuration": "AI Model Configuration"}, "common": {"as_default_model": "Set as De<PERSON>ult Model", "no_model_yet": "No model yet", "intelligent_questioning_platform": "Welcome to the SQLBot Intelligent Questioning Platform", "login": "<PERSON><PERSON>", "login_": "<PERSON><PERSON>", "confirm2": "Confirm", "excessive_tables_selected": "Excessive tables selected", "to_continue_saving": "The number of selected tables is 67, exceeding {msg}. This may cause the operation to time out or become unresponsive. Do you want to continue saving?", "proceed_with_caution": "Deletion is irreversible, so proceed with caution.", "sales_in_2024": "Delete conversation: {msg}?", "limited_to_30": "The number of data tables is limited to 30", "enter_your_password": "Please enter your password", "your_account_email_address": "Please enter your account/email address", "the_correct_password": "Please enter the correct password", "input_content": "Please Input Content", "may_not_exist": "No results yet, the user may not exist", "empty": " ", "back": "Back", "confirm": "Confirm", "cancel": "Cancel", "search": "Search", "system_manage": "System Management", "update_success": "Update", "save_success": "Save Successful", "next": "Next", "save": "Save", "logout": "Logout", "please_input": "Please enter {msg}", "switch_success": "Switch successfully", "result_count": "result", "clear_filter": "Clear conditions", "reset": "Reset", "simplified_chinese": "Simplified Chinese", "traditional_chinese": "Traditional Chinese", "help": "Help", "language": "Language", "re_upload": "Re-upload", "not_exceed_50mb": "Supports XLS, XLSX, CSV formats, file size does not exceed 50MB", "reset_password": "Reset password", "password_reset_successful": "Password reset successful", "or": "Or", "refresh": "Refresh"}, "dashboard": {"open_dashboard": "Open Dashboard", "add_dashboard_name_tips": "Please Input Dashboard Name", "existing_dashboard": "Existing Dashboard", "add_success": "Add Success", "no_data": "No Relevant Content Found", "new_tab": "New Tab", "length_limit64": "Field length must be between 1 and 64", "sort_column": "Sort field", "sort_type": "Sort type", "time": "Time", "sort_asc": "Ascending", "sort_desc": "Descending", "name_repeat": "Name Repeat", "rich_text_tips": "Double-click to enter text content", "exit_preview": "Exit preview", "no_chat": "No conversations", "today": "Today", "this_week": "This week", "earlier": "Earlier", "add_component_tips": "Select components from the top toolbar and add them here to create a dashboard", "add_view": "Add Chart", "delete_dashboard_warn": "Delete This Dashboard: {0}", "rename_dashboard": "Rename Dashboard", "dashboard_name": "Dashboard Name", "select_dashboard_tips": "Please select a dashboard on the left", "no_dashboard": "No Dashboards", "no_dashboard_info": "No content yet, click the button below to add", "search": "Search", "time_asc": "Sort by Time (Ascending)", "time_desc": "Sort by Time (Descending)", "name_asc": "Sort by Name (Ascending)", "name_desc": "Sort by Name (Descending)", "select_dashboard": "Please Select Dashboard", "name": "Name", "view": "View", "text": "Rich Text", "preview": "Preview", "creator": "Creator", "dashboard_id": "Dashboard ID", "create_time": "Create Time", "updator": "Updater", "update_time": "Update Time", "edit": "Edit", "edit_title": "Edit Title", "length_1_64_characters": "Name field length must be 1-64 characters", "rename": "<PERSON><PERSON>", "delete": "Delete", "delete_tips": "All resources in this folder will be deleted after deletion. Please proceed with caution.", "undo": "Undo", "reduction": "Rest<PERSON>", "folder": "Folder", "dashboard": "Dashboard", "delete_warning": "Are you sure you want to delete?", "delete_success": "Successfully deleted", "delete_resource_warn": "Are you sure you want to delete {0}?", "new_folder": "New Folder", "new_dashboard": "New Dashboard", "add_chart": "Add Chart", "chart_selected": "Selected {0}"}, "qa": {"new_chat": "New Chat", "start_sqlbot": "New Chat", "title": "Data Q&A", "placeholder": "Please enter your question", "ask": "Ask", "loading": "Loading...", "no_data": "No data available", "error": "An error occurred, please try again later", "question_placeholder": "Press Enter to submit, or use Ctrl + Enter for a new line", "greeting": "Hello, I'm SQLB<PERSON>. How can I assist you today?", "hint_description": "You can query data, generate charts, analyze data, predict trends, and more. Please select a data source to start your smart data exploration~", "select_datasource": "Select Data Source", "view_more": "View More", "selected_datasource": "Selected Data Source", "empty_datasource": "No data sources available. Please create one before starting Data Q&A!", "datasource_not_exist": "Data source does not exist", "guess_u_ask": "You might want to ask:", "continue_to_ask": "Continue asking:", "data_analysis": "Data Analysis", "data_predict": "Data Prediction", "chat_search": "Search", "thinking": "Thinking", "thinking_step": "Thought Process", "ask_again": "Regenerate", "today": "Today", "week": "This Week", "earlier": "Earlier", "no_time": "No Time", "rename_conversation_title": "Rename conversation title", "conversation_title": "Conversation title", "copied": "<PERSON>pied", "ask_failed": "Q&A failed"}, "ds": {"title": "Data Sources", "add": "Add Data Source", "delete": "Delete Data Source", "name": "Data Source Name", "type": "Data Source Type", "status": "Status", "actions": "Actions", "local_excelcsv": "Local Excel/CSV", "pieces_in_total": "Showing {ms} records", "test_connection": "Test Connection", "check": "Check", "connection_success": "Connection Successful", "connection_failed": "Connection Failed, Please Check Configuration", "Search Datasource": "Search Datasource", "tables": "Tables", "no_data_tip": "No data, select a table from left", "comment": "Comment", "table_schema": "Table Schema", "previous": "Previous", "preview": "Data Preview", "preview_tip": "Preview 100 items", "field": {"name": "Name", "type": "Type", "comment": "Comment", "custom_comment": "Custom Comment", "status": "Status"}, "edit": {"table_comment": "Edit Table Comment", "field_comment": "Edit Field Comment", "table_comment_label": "Table Comment", "field_comment_label": "Field Comment"}, "form": {"title": {"add": "Add Datasource", "edit": "Edit Datasource", "choose_tables": "Choose Tables"}, "base_info": "Base Info", "choose_tables": "Choose Tables", "name": "Name", "description": "Description", "host": "Host/Ip", "port": "Port", "username": "Username", "password": "Password", "database": "Database", "connect_mode": "Connect Mode", "service_name": "Service Name", "extra_jdbc": "Extra JDBC String", "schema": "<PERSON><PERSON><PERSON>", "get_schema": "<PERSON>", "file": "File", "upload_tip": "Only support .xls, .xlsx, .csv, size less than 50MB.", "version_tip": {"sqlServer": "Supported version: 2012+", "oracle": "Supported version: 12+", "mysql": "Supported version: 5.6+", "pg": "Supported version: 9.6+"}, "selected": "Selected: {0}/{1}", "validate": {"name_required": "Please input name", "name_length": "Length should be 1 to 50", "type_required": "Please choose database type", "host_required": "Please input host", "port_required": "Please input port", "database_required": "Please input database", "mode_required": "Please choose mode", "schema_required": "Please input schema"}, "mode": {"sid": "SID", "service_name": "Service Name"}, "support_version": "Supported version", "upload": {"button": "Upload", "tip": "Only support .xls, .xlsx, .csv, size less than 50MB."}, "connect": {"success": "Connect success", "failed": "Connect failed"}, "timeout": "Timeout(second)"}}, "datasource": {"data_source_yet": "No data source yet", "search_by_name": "Search by Name", "search": "Search", "all_types": "All types", "new_data_source": "New data source", "open_query": "New Chat", "edit": "Edit", "source_connection_failed": "Data source connection failed", "confirm": "Confirm", "enabled_status": "Enabled status", "custom_notes": "Custom notes", "field_type": "Field type", "field_name": "Field name", "copy": "Copy", "incorrect_email_format": "Incorrect email format", "the_original_one": "Are you sure you want to restore the password to the original one?", "relevant_content_found": "No relevant content found", "please_enter": "Please enter", "Please_select": "Please select", "table_notes": "Table notes", "field_notes": "Field notes", "select_all": "Select all", "mysql_data_source": "Modify {msg} data source", "configuration_information": "Configuration information", "data_source": "Do you want to delete the data source: {msg}?", "operate_with_caution": "After being deleted, the data source cannot be intelligently counted. Please operate with caution.", "data_source_de": "Unable to delete data source: {msg}", "cannot_be_deleted": "The chart of the dashboard uses this data source and cannot be deleted.", "got_it": "Got it", "field_original_notes": "Field original notes", "field_notes_1": "Field notes", "no_table": "No Table", "go_add": "Add"}, "model": {"default_model": "Default model", "model_type": "Model type", "basic_model": "Basic model", "set_successfully": "Set successfully", "operate_with_caution": "After the system default model is replaced, the result of intelligent question will be affected, please operate with caution.", "system_default_model": "Do you want to set {msg} as the system default model?", "ai_model_configuration": "AI model configuration", "system_default_model_de": "System default model", "relevant_results_found": "No relevant results found", "add_model": "Add model", "select_supplier": "Select supplier", "the_basic_model": "Please set a name for the basic model", "the_basic_model_de": "Please select the basic model", "model_name": "Model name", "custom_model_name": "Custom model name", "enter_to_add": "For models not listed in the list, just enter the model name and press Enter to add", "api_domain_name": "API domain name", "advanced_settings": "Advanced settings", "model_parameters": "Model parameters", "add": "Add", "parameters": "Parameter", "display_name": "Display name", "parameter_value": "Parameter value", "text": "Text", "number": "Number", "parameter_type": "Parameter type", "verification_successful": "Verification successful", "del_default_tip": "Unable to delete the model: {msg}", "del_default_warn": "This model is the system default model. Please set another model as the system default before deleting this model.", "del_warn_tip": "Would you like to remove the model: {msg}？", "check_failed": "Model is invalid [{msg}]", "default_miss": "The default llm has not been configured yet, so the q&a feature cannot be enabled, Please contact the administrator to configure it.", "default_miss_admin": "The default llm has not been configured yet, so the q&a feature cannot be enabled.", "to_config": "Go to configuration"}, "user": {"workspace": "Workspace", "creation_time": "Creation time", "user_source": "User source", "phone_number": "Phone number", "email": "Email", "user_status": "User status", "account": "Account", "name": "Name", "selected_2_items": "Selected {msg} items", "name_account_email": "Search name, account, email", "user_management": "User management", "filter": "Filter", "batch_import": "Batch import", "add_users": "Add users", "selected_2_users": "Do you want to delete the selected {msg} users?", "filter_conditions": "Filter conditions", "enable": "Enabled", "disable": "Disabled", "local_creation": "Local", "feishu": "<PERSON><PERSON><PERSON>", "dingtalk": "DingTalk", "wechat_for_business": "WeChat for business", "1240_results": "{msg} results", "clear_conditions": "Clear conditions", "disabled": "Disabled", "enabled": "Enabled", "edit_user": "Edit User", "del_user": "Do you want to delete user: {msg}?", "please_first": "Please first", "download_the_template": "download the template", "required_and_upload": ", fill in as required and upload", "file": "file", "upload_file": "Upload file", "xls_format_files": "Only supports xlsx and xls format files", "import": "Import", "change_file": "Change file", "data_import_completed": "Data import completed", "imported_100_data": "Successfully imported {msg} data", "return_to_view": "Return to view", "continue_importing": "Continue importing", "import_20_can": "Successfully imported {msg} data, failed to import {loss}, can", "download_error_report": "Download error report", "modify_and_re_import": ", modify and re-import", "data_import_failed": "Data import failed", "failed_100_can": "Import failed {msg}, can", "change_password": "Change password", "new_password": "New password", "confirm_password": "Confirm password", "old_password": "Old password", "title": "User management", "upgrade_pwd": {"title": "Change password", "old_pwd": "Old password", "new_pwd": "New password", "confirm_pwd": "Confirm password", "two_pwd_not_match": "The two passwords do not match"}}, "workspace": {"relevant_content_found": "No relevant content found", "add_workspace": "Add workspace", "administrator": "Administrator", "ordinary_member": "Member", "people": "People", "name_username_email": "Search name, account, email", "add_member": "Add member", "member_type": "Member type", "workspace_name": "Workspace name", "no_user": "No user", "remove": "Remove", "selected_2_members": "Do you want to remove the selected {msg} members?", "removed_successfully": "Removed successfully", "workspace_de_workspace": "Do you want to delete the workspace: {msg}?", "member_feng_yibudao": "Do you want to remove the member: {msg}?", "select_member": "Select member", "selected_2_people": "Selected: {msg} people", "clear": "Clear", "historical_dialogue": "No historical dialogue", "rename_a_workspace": "Rename a workspace", "return_to_workspace": "Return to workspace", "there_are": "There are", "2_dashboards": "{msg} dashboards", "smart_data_centers": "{msg} smart data centers", "confirm_to_delete": "After the data source is deleted, the related questions can no longer be asked and the charts in the dashboard cannot be displayed normally. Please operate with caution!", "id_account_to_add": "Search name/account", "find_user": "Find user", "add_successfully": "Add successfully", "member_management": "Member management", "permission_configuration": "Permission configuration", "set": "Settings", "operate_with_caution": "After deletion, the users under the workspace will be removed and all resources will be deleted. Please operate with caution."}, "permission": {"search_rule_group": "Search rule group", "add_rule_group": "Add rule group", "permission_rule": "Permission rules", "restricted_user": "Restricted user", "set_rule": "Set rule", "set_user": "Set user", "2": "{msg}", "238_people": "{msg} people", "no_permission_rule": "No permission rule", "set_permission_rule": "Set permission rule", "basic_information": "Basic information", "rule_group_name": "Rule group name", "rule_name": "Rule name", "type": "Type", "data_source": "Data source", "data_table": "Data table", "select_restricted_user": "Select restricted user", "rule_group_1": "Delete rule group: {msg}?", "no_rule": "No rule", "row_permission": "Row perm", "column_permission": "Column perm", "rule_rule_1": "Delete rule: {msg}?", "no_content": "No content", "edit_column_permission": "Edit column permission", "edit_row_permission": "Edit row permission", "add_column_permission": "Add column permission", "add_row_permission": "Add row permission", "no_fields_yet": "No fields yet", "filter_eq": "Equal to", "filter_not_eq": "Not equal to", "filter_lt": "Less than", "filter_le": "Less than or equal to", "filter_gt": "Greater than", "filter_ge": "Greater than or equal to", "filter_null": "Empty", "filter_not_null": "Not empty", "filter_empty": "Empty string", "filter_not_empty": "Not empty string", "filter_include": "Include", "filter_not_include": "Not included", "conditional_filtering": "Conditional filtering", "add_conditions": "Add conditions", "add_relationships": "Add relationships", "cannot_be_empty_": "Filter field cannot be empty", "cannot_be_empty_de_ruler": "Rule condition cannot be empty", "filter_value_can_null": "Filter value cannot be null", "filter_like": "Contains", "filter_not_like": "Does not contain", "filter_in": "Belongs to", "filter_not_in": "Does not belong to", "enter_a_question": "Please enter a question", "new_conversation": "New Conversation", "send": "Send", "stop_replying": "Stop replying", "i_am_sqlbot": "Hello, I am SQLBot", "predict_data_etc": "I can query data, generate charts, detect data anomalies, predict data, etc.", "intelligent_data_query": "Hurry up and start intelligent data query~"}, "embedded": {"embedded_management": "Embedded management", "embedded_assistant": "Embedded assistant", "embedded_page": "Embedded page", "no_application": "No application", "create_application": "Create application", "basic_application": "Basic application", "advanced_application": "Advanced application", "embed_third_party": "Embed third party", "support_is_required": "Applicable to scenarios where data verification is not required, just embed the embedded code into the third party code, no additional support is required", "data_permissions_etc": "Applicable to scenarios where data verification is required, requiring the third party system to provide data source interface, user interface data permissions, etc.", "create_basic_application": "Create basic application", "set_data_source": "Data source settings", "basic_information": "Basic information", "application_name": "Application name", "application_description": "Application description", "cross_domain_settings": "Cross-domain settings", "third_party_address": "Please enter the embedded third party address", "set_to_private": "Set as private", "set_to_public": "Set as public", "public": "Public", "private": "Private", "creating_advanced_applications": "Creating Advanced Applications", "configure_interface": "Configure interface", "interface_url": "Interface URL", "credential_acquisition_method": "Credential acquisition method", "table_notes": "Table notes", "system_credential_type": "Source system credential type", "credential_name": "Credential name", "target_credential_location": "Target credential location", "target_credential_name": "Target credential name", "target_credential": "Target credential", "edit_advanced_applications": "Edit advanced applications", "edit_basic_applications": "Edit basic applications", "delete": "delete {msg}?", "code_to_embed": "Copy the following code to embed", "floating_window_mode": "Floating window mode", "copy_successful": "Copy successful", "copy_failed": "Co<PERSON> failed", "open_the_query": "Public data sources allow all users to open the query; private data sources allow logged-in users to open the query", "add_interface_credentials": "Add interface credentials", "edit_interface_credentials": "Edit interface credentials", "duplicate_name": "Duplicate name", "duplicate_name_": "Duplicate name", "duplicate_account": "Duplicate account", "duplicate_email": "Duplicate email", "repeating_parameters": "Duplicate Parameter", "interface_credentials": "Interface credentials", "no_credentials_yet": "No credentials yet", "intelligent_customer_service": "SQLBot Intelligent Customer Service", "enter_a_question": "Please enter a question", "new_conversation": "New Conversation", "send": "Send", "stop_replying": "Stop replying", "i_am_sqlbot": "Hello, I am SQLBot", "predict_data_etc": "I can query data, generate charts, detect data anomalies, predict data, etc.", "intelligent_data_query": "Hurry up and start intelligent data query~", "origin_format_error": "Format invalid, starts with http or https and cannot end with /"}, "chat": {"type": "Chart Type", "chart_type": {"table": "Detail Table", "bar": "Bar", "column": "Column", "line": "Line", "pie": "Pie"}, "show_sql": "View SQL", "export_to": "Export As", "excel": "Excel", "picture": "Image", "add_to_dashboard": "Add to Dashboard", "full_screen": "Full Screen", "exit_full_screen": "Exit Full Screen", "sql_generation": "Generate SQL", "chart_generation": "Generate Chart", "inference_process": "Thought Process", "thinking": "Thinking", "data_analysis": "Data Analysis", "data_predict": "Data Prediction", "ds_is_invalid": "Datasource is invalid", "error": "Error", "no_data": "No Data", "show_error_detail": "Show error info"}, "about": {"title": "About", "auth_to": "Authorized to", "invalid_license": " License is invalid", "update_license": "Update License", "expiration_time": "Expiration Time", "expirationed": "(Expired)", "auth_num": "Authorized Number", "version": "Version", "version_num": "Version {0}", "standard": "Community Edition", "enterprise": "Enterprise Edition", "Professional": "Professional Edition", "Embedded": "Embedded Edition", "support": "Support", "update_success": "Update Successful", "serial_no": "Serial Number", "remark": "Remark", "back_community": "Restore to Community Edition", "confirm_tips": "Are you sure to restore to Community Edition?"}, "license": {"error_tips": "Do you want to refresh the page?", "offline_tips": "The service has been offline, please contact the administrator to restart the service!"}, "system": {"system_settings": "System Settings", "appearance_settings": "Appearance Settings", "platform_display_theme": "Platform Display Theme", "default_turquoise": "Default (Turquoise)", "tech_blue": "Tech Blue", "custom": "Custom", "platform_login_settings": "Platform Login Settings", "page_preview": "Page Preview", "restore_default": "<PERSON><PERSON>", "website_logo": "Website Logo", "tab": "Tab", "replace_image": "Replace Image", "larger_than_200kb": "Logo displayed at the top of the website: Recommended size: 48 x 48 pixels, supports JPG, PNG, and SVG, and no larger than 200KB", "login_logo": "<PERSON><PERSON>", "larger_than_200kb_de": "Logo on the right side of the login page: Recommended size: 204 x 52 pixels, supports JPG, PNG, and SVG, and no larger than 200KB", "login_background_image": "Login Background Image", "larger_than_5mb": "Background image on the left: Recommended size: 576 x 900 for vector images, 1152 x 1800 for bitmap images, supports JPG, PNG, and SVG, and no larger than 5MB", "website_name": "Website Name", "on_webpage_tabs": "Platform name displayed on webpage tabs", "welcome_message": "Welcome Message", "the_product_logo": "Welcome Message below the product logo", "screen_customization_supported": "Defaults to the SQLBot login screen; customization supported", "platform_settings": "Platform Settings", "help_documentation": "Help Documentation", "show_about": "Show About", "abort_update": "Abort Update", "save_and_apply": "Save and Apply", "setting_successfully": "Setting Successfully", "customize_theme_color": "Customize theme color"}}