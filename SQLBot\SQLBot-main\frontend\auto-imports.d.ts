/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const ElButton: typeof import('element-plus-secondary/es')['ElButton']
  const ElMessage: typeof import('element-plus-secondary/es')['ElMessage']
  const ElMessageBox: typeof import('element-plus-secondary/es')['ElMessageBox']
  const LicenseGenerator: any
}
