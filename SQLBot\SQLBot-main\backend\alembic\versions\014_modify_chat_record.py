"""modify_chat_record

Revision ID: fde8acca6143
Revises: bfa10ce83d73
Create Date: 2025-06-23 16:39:39.499180

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'fde8acca6143'
down_revision = 'bfa10ce83d73'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_record', sa.Column('predict_data', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_record', 'predict_data')
    # ### end Alembic commands ###
