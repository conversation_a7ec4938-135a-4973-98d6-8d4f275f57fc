name: build-and-push

run-name: 构建镜像并推送仓库 ${{ github.event.inputs.dockerImageTag }} (${{ github.event.inputs.registry }}) (${{ github.event.inputs.architecture }})

on:
  workflow_dispatch:
    inputs:
      dockerImageTag:
        description: 'Image Tag'
        default: 'v0.9.0'
        required: true
      dockerImageTagWithLatest:
        description: '是否发布latest tag（正式发版时选择，测试版本切勿选择）'
        default: false
        required: true
        type: boolean
      architecture:
        description: 'Architecture'
        required: true
        default: 'linux/amd64'
        type: choice
        options:
          - linux/amd64
          - linux/arm64
          - linux/amd64,linux/arm64
      registry:
        description: 'Push To Registry'
        required: true
        default: 'aliyun-registry'
        type: choice
        options:
          - aliyun-registry
          - dockerhub
          - dockerhub, aliyun-registry

jobs:
  build-and-push-to-aliyun-registry:
    if: ${{ contains(github.event.inputs.registry, 'aliyun') }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}
      - name: Prepare
        id: prepare
        run: |
          DOCKER_IMAGE=${{ secrets.ALIYUN_REGISTRY_HOST }}/dataease/sqlbot
          DOCKER_PLATFORMS=${{ github.event.inputs.architecture }}
          TAG_NAME=${{ github.event.inputs.dockerImageTag }}
          TAG_NAME_WITH_LATEST=${{ github.event.inputs.dockerImageTagWithLatest }}
          if [[ ${TAG_NAME_WITH_LATEST} == 'true' ]]; then            
            DOCKER_IMAGE_TAGS="--tag ${DOCKER_IMAGE}:${TAG_NAME} --tag ${DOCKER_IMAGE}:${TAG_NAME%%.*} --tag ${DOCKER_IMAGE}:latest"
          else
            DOCKER_IMAGE_TAGS="--tag ${DOCKER_IMAGE}:${TAG_NAME}"
          fi
          echo ::set-output name=buildx_args::--platform ${DOCKER_PLATFORMS} --memory-swap -1 \
            --build-arg DOCKER_IMAGE_TAG=${{ github.event.inputs.dockerImageTag }} --build-arg BUILD_AT=$(TZ=Asia/Shanghai date +'%Y-%m-%dT%H:%M') --build-arg GITHUB_COMMIT=`git rev-parse --short HEAD` --no-cache \
            ${DOCKER_IMAGE_TAGS} .
      - name: Set up Docker Buildx
        uses: crazy-max/ghaction-docker-buildx@v3
      - name: Login to Aliyun Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.ALIYUN_REGISTRY_HOST }}
          username: ${{ secrets.ALIYUN_REGISTRY_USERNAME }}
          password: ${{ secrets.ALIYUN_REGISTRY_PASSWORD }}
      - name: Docker Buildx (build-and-push)
        run: |
          docker buildx build --output "type=image,push=true" ${{ steps.prepare.outputs.buildx_args }}

  build-and-push-to-dockerhub:
    if: ${{ contains(github.event.inputs.registry, 'dockerhub') }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}
      - name: Prepare
        id: prepare
        run: |
          DOCKER_IMAGE=dataease/sqlbot
          DOCKER_PLATFORMS=${{ github.event.inputs.architecture }}
          TAG_NAME=${{ github.event.inputs.dockerImageTag }}          
          TAG_NAME_WITH_LATEST=${{ github.event.inputs.dockerImageTagWithLatest }}
          if [[ ${TAG_NAME_WITH_LATEST} == 'true' ]]; then            
            DOCKER_IMAGE_TAGS="--tag ${DOCKER_IMAGE}:${TAG_NAME} --tag ${DOCKER_IMAGE}:${TAG_NAME%%.*} --tag ${DOCKER_IMAGE}:latest"
          else
            DOCKER_IMAGE_TAGS="--tag ${DOCKER_IMAGE}:${TAG_NAME}"
          fi
          echo ::set-output name=buildx_args::--platform ${DOCKER_PLATFORMS} --memory-swap -1 \
            --build-arg DOCKER_IMAGE_TAG=${{ github.event.inputs.dockerImageTag }} --build-arg BUILD_AT=$(TZ=Asia/Shanghai date +'%Y-%m-%dT%H:%M') --build-arg GITHUB_COMMIT=`git rev-parse --short HEAD` --no-cache \
            ${DOCKER_IMAGE_TAGS} .
      - name: Set up Docker Buildx
        uses: crazy-max/ghaction-docker-buildx@v3
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Docker Buildx (build-and-push)
        run: |
          docker buildx build --output "type=image,push=true" ${{ steps.prepare.outputs.buildx_args }}