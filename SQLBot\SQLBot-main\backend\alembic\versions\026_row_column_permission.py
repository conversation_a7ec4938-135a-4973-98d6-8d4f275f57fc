"""026_row_column_permission

Revision ID: 4c6d18a18bd4
Revises: 863105882eba
Create Date: 2025-06-25 17:32:09.183257

"""
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4c6d18a18bd4'
down_revision = '97dcdbedaaf3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ds_permission',
                    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
                    sa.Column('enable', sa.<PERSON>(), nullable=False),
                    sa.Column('auth_target_type', sa.String(128), nullable=False),
                    sa.Column('auth_target_id', sa.BigInteger(), nullable=True),
                    sa.Column('type', sa.String(64), nullable=False),
                    sa.Column('ds_id', sa.<PERSON>ger(), nullable=True),
                    sa.Column('table_id', sa.BigInteger(), nullable=True),
                    sa.Column('expression_tree', sa.Text(), nullable=True),
                    sa.Column('permissions', sa.Text(), nullable=True),
                    sa.Column('white_list_user', sa.Text(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    op.create_table('ds_rules',
                    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
                    sa.Column('enable', sa.Boolean(), nullable=False),
                    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
                    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=512), nullable=True),
                    sa.Column('permission_list', sa.Text(), nullable=True),
                    sa.Column('user_list', sa.Text(), nullable=True),
                    sa.Column('white_list_user', sa.Text(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.PrimaryKeyConstraint('id')
                    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('ds_rules')
    op.drop_table('ds_permission')
    # ### end Alembic commands ###
