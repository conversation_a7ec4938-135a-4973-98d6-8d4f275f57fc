"""023_modify_chat_record

Revision ID: f535d09946f6
Revises: e6b20ae73606
Create Date: 2025-07-11 15:36:18.473133

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f535d09946f6'
down_revision = 'e6b20ae73606'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chat_record', 'token_sql',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=256),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_chart',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=256),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_analysis',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=256),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_predict',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=256),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_recommended_question',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=256),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_select_datasource_question',
               existing_type=sa.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=256),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('chat_record', 'token_select_datasource_question',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=256),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_recommended_question',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=256),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_predict',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=256),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_analysis',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=256),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_chart',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=256),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('chat_record', 'token_sql',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=256),
               type_=sa.INTEGER(),
               existing_nullable=True)
    # ### end Alembic commands ###
