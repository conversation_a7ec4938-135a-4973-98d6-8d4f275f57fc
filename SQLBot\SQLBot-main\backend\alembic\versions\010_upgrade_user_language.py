"""010_upgrade_user_language

Revision ID: 8dc3b1bdbfef
Revises: 804b08ac329d
Create Date: 2025-06-10 11:21:35.257604

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8dc3b1bdbfef'
down_revision = '804b08ac329d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.add_column('sys_user', sa.Column('language', sa.VARCHAR(length=255), server_default=sa.text("'zh-CN'"), nullable=False))
   
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.drop_column('sys_user', 'language')
   
    # ### end Alembic commands ###
