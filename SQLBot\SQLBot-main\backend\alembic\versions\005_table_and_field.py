"""005_table_and_field

Revision ID: 0a6f11be9be4
Revises: 8fe654655905
Create Date: 2025-05-15 10:20:25.686576

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '0a6f11be9be4'
down_revision = '8fe654655905'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('core_field',
    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
    sa.Column('ds_id', sa.BigInteger(), nullable=True),
    sa.Column('table_id', sa.BigInteger(), nullable=True),
    sa.Column('checked', sa.<PERSON>(), nullable=False),
    sa.Column('field_name', sa.Text(), nullable=True),
    sa.Column('field_type', sqlmodel.sql.sqltypes.AutoString(length=128), nullable=True),
    sa.Column('field_comment', sa.Text(), nullable=True),
    sa.Column('custom_comment', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('core_table',
    sa.Column('id', sa.Integer(), sa.Identity(always=True), nullable=False),
    sa.Column('ds_id', sa.BigInteger(), nullable=True),
    sa.Column('checked', sa.Boolean(), nullable=False),
    sa.Column('table_name', sa.Text(), nullable=True),
    sa.Column('table_comment', sa.Text(), nullable=True),
    sa.Column('custom_comment', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('core_table')
    op.drop_table('core_field')
    # ### end Alembic commands ###
