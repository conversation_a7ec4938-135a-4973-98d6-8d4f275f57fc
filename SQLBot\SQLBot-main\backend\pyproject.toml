[project]
name = "sqlbot"
version = "1.0.0"
description = ""
requires-python = ">=3.10,<3.13"
dependencies = [
    "fastapi[standard]<1.0.0,>=0.114.2",
    "python-multipart<1.0.0,>=0.0.7",
    "passlib[bcrypt]<2.0.0,>=1.7.4",
    "tenacity<9.0.0,>=8.2.3",
    "pydantic>2.0",
    "alembic<2.0.0,>=1.12.1",
    "httpx<1.0.0,>=0.25.1",
    "psycopg[binary]<4.0.0,>=3.1.13",
    "sqlmodel<1.0.0,>=0.0.21",
    # Pin bcrypt until passlib supports the latest
    "bcrypt==4.0.1",
    "pydantic-settings<3.0.0,>=2.2.1",
    "sentry-sdk[fastapi]<2.0.0,>=1.40.6",
    "pyjwt<3.0.0,>=2.8.0",
    "pycryptodome (>=3.22.0,<4.0.0)",
    "langchain>=0.3,<0.4",
    "langchain-core>=0.3,<0.4",
    "langchain-openai>=0.3,<0.4",
    "langchain-community>=0.3,<0.4",
    "langgraph>=0.3,<0.4",
    "dashscope>=1.14.0,<2.0.0",
    "pymysql (>=1.1.1,<2.0.0)",
    "cryptography (>=44.0.3,<45.0.0)",
    "llama_index>=0.12.35",
    "pymssql (>=2.3.4,<3.0.0)",
    "pandas (>=2.2.3,<3.0.0)",
    "openpyxl (>=3.1.5,<4.0.0)",
    "psycopg2-binary (>=2.9.10,<3.0.0)",
    "oracledb (>=3.1.1,<4.0.0)",
    "pyyaml (>=6.0.2,<7.0.0)",
    "fastapi-mcp (>=0.3.4,<0.4.0)",
    "tabulate>=0.9.0",
    "sqlbot-xpack==0.0.3.13",
    "fastapi-cache2>=0.2.2",
    "sqlparse>=0.5.3",
    "redis>=6.2.0",
    "xlsxwriter>=3.2.5",
    "python-calamine>=0.4.0",
]
[[tool.uv.index]]
name = "default"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true

[[tool.uv.index]]
name = "testpypi"
url = "https://test.pypi.org/simple"
explicit = true

[tool.uv.sources]
sqlbot-xpack = { index = "testpypi" }

[tool.uv]
dev-dependencies = [
    "pytest<8.0.0,>=7.4.3",
    "mypy<2.0.0,>=1.8.0",
    "ruff<1.0.0,>=0.2.2",
    "pre-commit<4.0.0,>=3.6.2",
    "types-passlib<2.0.0.0,>=1.7.7.20240106",
    "coverage<8.0.0,>=7.4.3",
]

[[tool.poetry.source]]
name = "mirrors"
url = "http://mirrors.aliyun.com/pypi/simple"
priority = "primary"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.mypy]
strict = true
exclude = ["venv", ".venv", "alembic"]

[tool.ruff]
target-version = "py310"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "W191",  # indentation contains tabs
    "B904",  # Allow raising exceptions without from e, for HTTPException
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true
